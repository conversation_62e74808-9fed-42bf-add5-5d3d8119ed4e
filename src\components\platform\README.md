# Platform 组件库

这个目录包含了平台相关的通用组件。

## ProjectList 组件

项目列表展示组件，用于展示视频编辑项目的列表数据。

### 功能特性

- 📋 支持项目列表展示
- 🔄 支持加载状态显示
- 📱 响应式设计，适配不同屏幕
- 🎨 现代化卡片式布局
- ⚡ 支持点击交互和操作按钮
- 🕒 智能时间格式化显示
- 🏷️ 状态标签展示
- 📷 项目封面图片展示
- 🚫 空状态友好提示

### 使用方法

```vue
<template>
  <ProjectList 
    :project-list="projectList"
    :loading="loading"
    @item-click="handleItemClick"
    @edit="handleEdit"
    @delete="handleDelete"
    @create="handleCreate"
  />
</template>

<script setup>
import ProjectList from '@/components/platform/project-list.vue'
import { ref } from 'vue'

const projectList = ref([
  {
    "Status": "Produced", 
    "ModifiedSource": "WebSDK", 
    "ModifiedTime": "2025-07-30T04:03:37.244+00:00", 
    "CreateTime": "2025-07-30T01:43:01.570+00:00", 
    "CreateSource": "OpenAPI", 
    "ProjectType": "EditingProject", 
    "ProjectId": "9cf8506a756d43e79323d28f3858d664", 
    "Title": "Editing Project Created at 2025-07-30 09:43:01.57", 
    "TemplateType": "Timeline" 
  }
])

const loading = ref(false)

// 事件处理
function handleItemClick(item) {
  console.log('点击项目:', item)
}

function handleEdit(item) {
  console.log('编辑项目:', item)
}

function handleDelete(item) {
  console.log('删除项目:', item)
}

function handleCreate() {
  console.log('创建新项目')
}
</script>
```

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| projectList | ProjectInfo[] | [] | 项目列表数据 |
| loading | boolean | false | 是否显示加载状态 |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| item-click | 点击项目列表项时触发 | (item: ProjectInfo) |
| edit | 点击编辑按钮时触发 | (item: ProjectInfo) |
| delete | 点击删除按钮时触发 | (item: ProjectInfo) |
| create | 点击创建按钮时触发 | - |

### ProjectInfo 数据结构

```typescript
interface ProjectInfo {
  ProjectId: string          // 项目ID
  Title: string             // 项目标题
  Description?: string      // 项目描述
  Status: number | string   // 项目状态（支持数字或字符串）
  CoverURL?: string        // 封面图片URL
  CreateTime?: string      // 创建时间（可选）
  ModifiedTime?: string    // 修改时间（可选）
  CreateSource?: string    // 创建来源
  ModifiedSource?: string  // 修改来源
  ProjectType?: string     // 项目类型
  TemplateType?: string    // 模板类型
  Timeline: any            // 时间轴对象（必需）
}
```

### 状态类型

组件支持以下状态类型，会自动显示对应的标签颜色：

- `Draft` - 草稿 (灰色)
- `Editing` - 编辑中 (蓝色)
- `Producing` - 制作中 (橙色)
- `Produced` - 已制作完成 (绿色)
- `ProduceFailed` - 制作失败 (红色)
- `Normal` - 正常 (绿色)

### 样式特色

- 卡片式布局，圆角设计
- 悬停和点击交互效果
- 状态标签使用不同颜色区分
- 时间显示智能格式化（刚刚、几分钟前、几小时前等）
- 空状态提示友好
- 加载状态动画

### 注意事项

1. 组件依赖 `uview-plus` UI 库的相关组件
2. 需要引入项目中的 `parseTime` 工具函数
3. 图标字体需要在项目中配置相应的 iconfont
4. 组件使用了 TypeScript，建议在 TypeScript 项目中使用
