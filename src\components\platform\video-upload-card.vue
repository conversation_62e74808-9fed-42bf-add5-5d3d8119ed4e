<template>
  <view class="card upload-card">
    <view class="card-header">
      <view class="card-title">
        <text class="title-icon">📹</text>
        <text class="title-text">{{ title }}</text>
      </view>
      <text class="card-subtitle">{{ subtitle }}</text>
    </view>
    <view class="card-content">
      <!-- 上传区域 -->
      <view class="upload-area">
        <!-- 主上传按钮 -->
        <view class="main-upload-btn" @click="triggerUpload" v-if="videoList.length === 0">
          <view class="upload-icon-large">
            <text class="icon">🎬</text>
          </view>
          <view class="upload-content">
            <text class="upload-title">{{ uploadText }}</text>
            <text class="upload-desc">{{ uploadDesc }}</text>
            <text class="upload-limit">最多可上传 {{ maxCount }} 个视频</text>
          </view>
        </view>

        <!-- 视频列表 -->
        <view class="video-list" v-if="videoList.length > 0">
          <view class="video-grid">
            <view 
              v-for="(item, index) in videoList" 
              :key="index" 
              class="video-item"
            >
              <view class="video-preview" @click="handlePreview(item, index)">
                <image
                  v-if="item.thumb"
                  :src="item.thumb"
                  class="video-thumbnail"
                  mode="aspectFill"
                />
                <view v-else class="video-placeholder">
                  <text class="placeholder-icon">🎥</text>
                </view>
                <view class="video-overlay">
                  <text class="play-icon">▶️</text>
                </view>
                <view class="delete-btn" @click.stop="handleDelete(item, index)">
                  <text class="delete-icon">✕</text>
                </view>
              </view>
              <view class="video-info">
                <text class="video-name">{{ item.name || `视频${index + 1}` }}</text>
                <text class="video-size">{{ formatFileSize(item.size) }}</text>
              </view>
            </view>

            <!-- 添加更多按钮 -->
            <view 
              v-if="videoList.length < maxCount" 
              class="add-more-btn"
              @click="triggerUpload"
            >
              <view class="add-icon">
                <text>➕</text>
              </view>
              <text class="add-text">添加视频</text>
            </view>
          </view>

          <!-- 上传进度和状态 -->
          <view class="upload-progress" v-if="isUploading">
            <view class="progress-bar">
              <view class="progress-fill" :style="{ width: uploadProgress + '%' }"></view>
            </view>
            <text class="progress-text">上传中... {{ uploadProgress }}%</text>
          </view>
        </view>

        <!-- 隐藏的上传组件 -->
        <up-upload
          ref="uploadRef"
          :fileList="videoList"
          @afterRead="handleAfterRead"
          @delete="handleUploadDelete"
          multiple
          :maxCount="maxCount"
          accept="video"
          :disabled="disabled"
          style="display: none;"
        />
      </view>

      <!-- 上传统计 -->
      <view class="upload-stats" v-if="videoList.length > 0 && showStats">
        <view class="stats-item">
          <text class="stats-label">已上传</text>
          <text class="stats-value">{{ videoList.length }}/{{ maxCount }}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">总大小</text>
          <text class="stats-value">{{ getTotalSize() }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { UploadInstance, VideoFile, VideoUploadCardProps } from '@/types/upload'

// Props 定义
interface Props extends VideoUploadCardProps {}

const props = withDefaults(defineProps<Props>(), {
  title: '上传视频',
  subtitle: '上传您的视频素材，支持多个文件',
  uploadText: '点击上传视频',
  uploadDesc: '支持 MP4、MOV、AVI 等格式',
  maxCount: 5,
  disabled: false,
  showStats: true,
  isUploading: false,
  uploadProgress: 0
})

// Events 定义
const emit = defineEmits<{
  'update:modelValue': [value: VideoFile[]]
  'afterRead': [file: VideoFile]
  'delete': [file: VideoFile, index: number]
  'upload': []
  'preview': [file: VideoFile, index: number]
}>()

// 响应式数据
const uploadRef = ref<UploadInstance | null>(null)

// 计算属性
const videoList = computed({
  get: () => props.modelValue || [],
  set: (value) => emit('update:modelValue', value)
})

// 方法
const triggerUpload = () => {
  if (uploadRef.value && !props.disabled) {
    try {
      uploadRef.value.chooseFile()
    } catch (error) {
      console.error('触发上传失败:', error)
    }
  }
  emit('upload')
}

const handleAfterRead = (file: VideoFile) => {
  emit('afterRead', file)
}

// 处理自定义删除按钮的点击
const handleDelete = (file: VideoFile, index: number) => {
  console.log('自定义删除按钮点击:', file.name, '索引:', index)

  // 只发出删除事件给父组件，让父组件决定是否删除
  emit('delete', file, index)
}

// 处理 up-upload 组件的删除事件
const handleUploadDelete = (file: any, index: number) => {
  console.log('up-upload 删除事件:', file, '索引:', index)

  // 将 up-upload 的删除事件转发给父组件
  emit('delete', file, index)
}

const handlePreview = (file: VideoFile, index: number) => {
  // 发出预览事件，让父组件处理
  emit('preview', file, index)

  // 如果有视频URL，尝试预览播放
  if (file.url || file.path) {
    const videoUrl = file.url || file.path
    if (videoUrl) {
      try {
        // 使用 uni.previewMedia API（支持多平台）
        uni.previewMedia({
          sources: [{
            url: videoUrl,
            type: 'video'
          }],
          current: 0,
          success: () => {
            console.log('视频预览成功')
          },
          fail: (err) => {
            console.error('视频预览失败:', err)
            // 降级处理：显示提示信息
            uni.showModal({
              title: '预览提示',
              content: `视频文件: ${file.name}\n大小: ${formatFileSize(file.size)}\n点击确定尝试其他方式播放`,
              success: (res) => {
                if (res.confirm) {
                  // 尝试使用系统默认方式打开
                  // #ifdef H5
                  if (typeof window !== 'undefined') {
                    window.open(videoUrl, '_blank')
                  }
                  // #endif
                }
              }
            })
          }
        })
      } catch (error) {
        console.error('预览视频时出错:', error)
        uni.showToast({
          title: '暂不支持预览此格式',
          icon: 'none'
        })
      }
    }
  } else {
    uni.showToast({
      title: '视频文件不可预览',
      icon: 'none',
      duration: 2000
    })
  }
}

// 格式化文件大小
const formatFileSize = (size: number | undefined) => {
  if (!size) return '0B'
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  let fileSize = size
  
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }
  
  return `${fileSize.toFixed(1)}${units[index]}`
}

// 获取总文件大小
const getTotalSize = () => {
  const totalBytes = videoList.value.reduce((total, item) => total + (item.size || 0), 0)
  return formatFileSize(totalBytes)
}
</script>

<style lang="scss" scoped>
// 卡片通用样式
.card {
  background: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.3s ease;

  .card-header {
    padding: 32rpx 32rpx 24rpx;
    border-bottom: 1rpx solid #f1f5f9;

    .card-title {
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;

      .title-icon {
        font-size: 32rpx;
        margin-right: 16rpx;
      }

      .title-text {
        font-size: 32rpx;
        font-weight: 600;
        color: #1e293b;
        line-height: 1.2;
      }
    }

    .card-subtitle {
      font-size: 26rpx;
      color: #64748b;
      line-height: 1.4;
    }
  }

  .card-content {
    padding: 32rpx;
  }
}

// 上传区域样式
.upload-area {
  // 主上传按钮
  .main-upload-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx 40rpx;
    border: 3rpx dashed #e2e8f0;
    border-radius: 20rpx;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transition: all 0.3s ease;
    cursor: pointer;

    &:active {
      border-color: #3b82f6;
      background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
      transform: scale(0.98);
    }

    .upload-icon-large {
      width: 120rpx;
      height: 120rpx;
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 32rpx;
      box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.3);

      .icon {
        font-size: 60rpx;
        color: #ffffff;
      }
    }

    .upload-content {
      text-align: center;

      .upload-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 12rpx;
        display: block;
      }

      .upload-desc {
        font-size: 26rpx;
        color: #64748b;
        margin-bottom: 8rpx;
        display: block;
      }

      .upload-limit {
        font-size: 24rpx;
        color: #94a3b8;
        display: block;
      }
    }
  }

  // 视频列表
  .video-list {
    .video-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));
      gap: 24rpx;
      margin-bottom: 32rpx;
    }

    .video-item {
      .video-preview {
        position: relative;
        width: 100%;
        height: 160rpx;
        border-radius: 16rpx;
        overflow: hidden;
        background: #f1f5f9;
        margin-bottom: 12rpx;

        .video-thumbnail {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .video-placeholder {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);

          .placeholder-icon {
            font-size: 48rpx;
            color: #64748b;
          }
        }

        .video-overlay {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 60rpx;
          height: 60rpx;
          background: rgba(0, 0, 0, 0.6);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          .play-icon {
            font-size: 24rpx;
            color: #ffffff;
            margin-left: 4rpx;
          }
        }

        .delete-btn {
          position: absolute;
          top: 8rpx;
          right: 8rpx;
          width: 40rpx;
          height: 40rpx;
          background: rgba(239, 68, 68, 0.9);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          backdrop-filter: blur(4rpx);

          .delete-icon {
            font-size: 20rpx;
            color: #ffffff;
            font-weight: bold;
          }
        }
      }

      .video-info {
        .video-name {
          font-size: 24rpx;
          color: #374151;
          font-weight: 500;
          display: block;
          margin-bottom: 4rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .video-size {
          font-size: 22rpx;
          color: #9ca3af;
          display: block;
        }
      }
    }

    .add-more-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 160rpx;
      border: 2rpx dashed #cbd5e1;
      border-radius: 16rpx;
      background: #f8fafc;
      transition: all 0.3s ease;
      cursor: pointer;

      &:active {
        border-color: #3b82f6;
        background: #eff6ff;
        transform: scale(0.95);
      }

      .add-icon {
        font-size: 40rpx;
        color: #64748b;
        margin-bottom: 8rpx;
      }

      .add-text {
        font-size: 24rpx;
        color: #64748b;
        font-weight: 500;
      }
    }

    // 上传进度
    .upload-progress {
      margin-top: 24rpx;
      padding: 20rpx;
      background: #f0f9ff;
      border-radius: 12rpx;
      border: 1rpx solid #e0f2fe;

      .progress-bar {
        width: 100%;
        height: 8rpx;
        background: #e0f2fe;
        border-radius: 4rpx;
        overflow: hidden;
        margin-bottom: 12rpx;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
          border-radius: 4rpx;
          transition: width 0.3s ease;
        }
      }

      .progress-text {
        font-size: 24rpx;
        color: #0369a1;
        font-weight: 500;
        text-align: center;
        display: block;
      }
    }
  }
}

// 上传统计
.upload-stats {
  margin-top: 32rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 16rpx;
  border: 1rpx solid #bae6fd;
  display: flex;
  justify-content: space-around;

  .stats-item {
    text-align: center;

    .stats-label {
      font-size: 24rpx;
      color: #0369a1;
      font-weight: 500;
      display: block;
      margin-bottom: 8rpx;
    }

    .stats-value {
      font-size: 28rpx;
      color: #0c4a6e;
      font-weight: 700;
      display: block;
    }
  }
}
</style>
