<template>
  <view class="video-player-page">
    <up-navbar
      title="视频播放"
      :safeAreaInsetTop="true"
      leftIcon="arrow-left"
      leftText="返回"
      @leftClick="goBack"
    />
    
    <view class="player-container">
      <video
        :src="videoUrl"
        :poster="posterUrl"
        controls
        :show-center-play-btn="true"
        :show-play-btn="true"
        :show-fullscreen-btn="true"
        :show-progress="true"
        :enable-progress-gesture="true"
        object-fit="contain"
        @error="onVideoError"
        @play="onVideoPlay"
        @pause="onVideoPause"
        @ended="onVideoEnded"
        @timeupdate="onTimeUpdate"
        @fullscreenchange="onFullscreenChange"
      ></video>
      
      <view class="video-info" v-if="videoName">
        <text class="video-title">{{ videoName }}</text>
      </view>
      
      <view class="error-info" v-if="hasError">
        <text class="error-text">视频加载失败</text>
        <text class="error-desc">请检查视频文件是否存在或格式是否支持</text>
        <up-button 
          type="primary" 
          size="small" 
          @click="retryLoad"
          style="margin-top: 20rpx;"
        >
          重新加载
        </up-button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const videoUrl = ref('')
const videoName = ref('')
const posterUrl = ref('')
const hasError = ref(false)

onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}
  
  console.log('视频播放页面参数:', options)
  
  if (options.url) {
    videoUrl.value = decodeURIComponent(options.url)
  }
  
  if (options.name) {
    videoName.value = decodeURIComponent(options.name)
  }
  
  if (options.poster) {
    posterUrl.value = decodeURIComponent(options.poster)
  }
  
  if (!videoUrl.value) {
    hasError.value = true
    uni.showToast({
      title: '视频地址无效',
      icon: 'none'
    })
  }
})

const goBack = () => {
  uni.navigateBack()
}

const onVideoError = (e) => {
  console.error('视频播放错误:', e)
  hasError.value = true
  uni.showToast({
    title: '视频播放失败',
    icon: 'none'
  })
}

const onVideoPlay = () => {
  console.log('视频开始播放')
  hasError.value = false
}

const onVideoPause = () => {
  console.log('视频暂停')
}

const onVideoEnded = () => {
  console.log('视频播放结束')
}

const onTimeUpdate = (e) => {
  // console.log('播放进度更新:', e.detail)
}

const onFullscreenChange = (e) => {
  console.log('全屏状态变化:', e.detail)
}

const retryLoad = () => {
  hasError.value = false
  // 强制重新加载视频
  const tempUrl = videoUrl.value
  videoUrl.value = ''
  setTimeout(() => {
    videoUrl.value = tempUrl
  }, 100)
}
</script>

<style lang="scss" scoped>
.video-player-page {
  min-height: 100vh;
  background: #000;
}

.player-container {
  position: relative;
  width: 100%;
  height: calc(100vh - var(--status-bar-height) - 44px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

video {
  width: 100%;
  height: 60vh;
  background: #000;
}

.video-info {
  position: absolute;
  bottom: 100rpx;
  left: 0;
  right: 0;
  padding: 0 40rpx;
  text-align: center;
}

.video-title {
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.5);
}

.error-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  padding: 40rpx;
  background: rgba(0,0,0,0.8);
  border-radius: 16rpx;
}

.error-text {
  color: #ff4757;
  font-size: 32rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 16rpx;
}

.error-desc {
  color: #ccc;
  font-size: 26rpx;
  display: block;
  line-height: 1.5;
}
</style>
