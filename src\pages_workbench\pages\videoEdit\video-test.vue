<template>
  <view class="test-page">
    <up-navbar
      title="视频播放测试"
      :safeAreaInsetTop="true"
      leftIcon="arrow-left"
      leftText="返回"
      @leftClick="goBack"
    />
    
    <view class="container">
      <view class="test-section">
        <text class="section-title">视频上传测试</text>
        <VideoUploadCard
          v-model="testVideoList"
          title="测试视频上传"
          subtitle="测试视频播放功能"
          @after-read="handleAfterRead"
          @delete="handleDelete"
          @preview="handlePreview"
        />
      </view>
      
      <view class="debug-section">
        <text class="section-title">调试信息</text>
        <view class="debug-info">
          <text class="debug-text">视频数量: {{ testVideoList.length }}</text>
          <view v-for="(video, index) in testVideoList" :key="index" class="video-debug">
            <text class="debug-text">视频 {{ index + 1 }}:</text>
            <text class="debug-text">名称: {{ video.name }}</text>
            <text class="debug-text">大小: {{ formatSize(video.size) }}</text>
            <text class="debug-text">URL: {{ video.url || '无' }}</text>
            <text class="debug-text">路径: {{ video.path || '无' }}</text>
            <text class="debug-text">类型: {{ video.type || '无' }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import VideoUploadCard from '@/components/platform/video-upload-card.vue'

const testVideoList = ref([])

const goBack = () => {
  uni.navigateBack()
}

const handleAfterRead = (event) => {
  console.log('测试页面 - 文件上传:', event)
  const { file } = event
  const fileList = Array.isArray(file) ? file : [file]
  
  fileList.forEach((item) => {
    console.log('测试页面 - 处理文件:', item)
    testVideoList.value.push({
      ...item,
      name: item.name || `测试视频_${Date.now()}`,
      url: item.url || item.path
    })
  })
  
  console.log('测试页面 - 当前列表:', testVideoList.value)
}

const handleDelete = (file, index) => {
  console.log('测试页面 - 删除文件:', file, index)
  testVideoList.value.splice(index, 1)
}

const handlePreview = (file, index) => {
  console.log('测试页面 - 预览视频:', file, index)
  
  const videoUrl = file.url || file.path
  if (!videoUrl) {
    uni.showToast({
      title: '视频路径无效',
      icon: 'none'
    })
    return
  }
  
  // 直接使用uni.previewMedia
  uni.previewMedia({
    sources: [{
      url: videoUrl,
      type: 'video'
    }],
    current: 0,
    success: () => {
      console.log('预览成功')
    },
    fail: (err) => {
      console.error('预览失败:', err)
      uni.showModal({
        title: '预览失败',
        content: `错误信息: ${JSON.stringify(err)}\n\n视频路径: ${videoUrl}`,
        showCancel: false
      })
    }
  })
}

const formatSize = (size) => {
  if (!size) return '0B'
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  let fileSize = size
  
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }
  
  return `${fileSize.toFixed(1)}${units[index]}`
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.container {
  padding: 20rpx;
}

.test-section, .debug-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.debug-info {
  background: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.debug-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  display: block;
  margin-bottom: 10rpx;
}

.video-debug {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #007aff;
}
</style>
