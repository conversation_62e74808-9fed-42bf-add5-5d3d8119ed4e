<template>
    <view class="confirm-dialog" v-if="visible">
      <view class="dialog-mask"></view>
      <view class="dialog-content">
        <view class="dialog-header">
          <view class="header-icon" v-if="showIcon">
            <text class="icon" :style="{ color: iconColor }">{{ icon }}</text>
          </view>
          <text class="header-text">{{ headerText }}</text>
        </view>
        <view class="dialog-title">
          <view v-if="showFileInfo" class="file-info">
            <view class="file-name-container">
              <text class="file-label">文件名</text>
              <text class="file-name" :title="fullFileName">{{ displayFileName }}</text>
            </view>
            <view class="file-warning">{{ warningText }}</view>
          </view>
          <view v-else>{{ title }}</view>
        </view>
        <view class="dialog-buttons">
          <button 
            class="button cancel-btn" 
            @click="handleCancel"
            hover-class="button-hover"
          >{{ cancelText }}</button>
          <button 
            class="button confirm-btn" 
            @click="handleConfirm"
            hover-class="button-hover"
            :style="{ background: color }"
          >{{ confirmText }}</button>
        </view>
      </view>
    </view>
</template>
  
<script setup>
import { defineProps, defineEmits } from 'vue'
  
const props = defineProps({
  visible: {
      type: Boolean,
      default: false
  },
  title: {
      type: String,
      default: '提示'
  },
  headerText: {
      type: String,
      default: '提示'
  },
  showIcon: {
      type: Boolean,
      default: false
  },
  icon: {
      type: String,
      default: '⚠️'
  },
  iconColor: {
      type: String,
      default: '#ff4757'
  },
  cancelText: {
      type: String,
      default: '取消'
  },
  confirmText: {
      type: String,
      default: '确认'
  },
  color: {
      type: String,
      default: '#1E88E5'
  },
  showFileInfo: {
      type: Boolean,
      default: false
  },
  mainQuestion: {
      type: String,
      default: '确定要删除这个文件吗？'
  },
  fullFileName: {
      type: String,
      default: ''
  },
  displayFileName: {
      type: String,
      default: ''
  },
  warningText: {
      type: String,
      default: '删除后无法恢复'
  }
})
  
const emit = defineEmits(['update:visible', 'confirm', 'cancel']);
    
const handleConfirm = () => {
  emit('update:visible', false);
  emit('confirm')
}
  
const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false);
}
  </script>
  
<style lang="scss" scoped>
.confirm-dialog {
.dialog-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 999;
}

.dialog-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 560rpx;
    max-width: 90vw;
    min-width: 480rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    z-index: 1000;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
    animation: dialogShow 0.3s ease-out;

    .dialog-header {
        padding: 20rpx 20rpx 10rpx;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16rpx;

        .header-icon {
          .icon {
            font-size: 60rpx;
            line-height: 1;
          }
        }

        .header-text {
          font-size: 32rpx;
          color: #333;
          font-weight: 600;
        }
    }
    .dialog-title {
        padding: 10rpx 40rpx 40rpx;
        text-align: center;
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
        white-space: pre-line;
        word-wrap: break-word;
        word-break: break-all;
        max-height: 300rpx;
        overflow-y: auto;
        box-sizing: border-box;

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6rpx;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 6rpx;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 6rpx;

          &:hover {
            background: #a8a8a8;
          }
        }

        // 文件信息样式
        .file-info {
          text-align: center;
          padding: 0 20rpx;

          .file-question {
            font-size: 32rpx;
            color: #333;
            font-weight: 600;
            margin-bottom: 32rpx;
            text-align: center;
            line-height: 1.4;
          }

          .file-name-container {
            background: #f8f9fa;
            padding: 24rpx 20rpx;
            border-radius: 12rpx;
            margin: 0 auto 24rpx;
            text-align: center;
            border: 1rpx solid #e9ecef;
            max-width: 100%;

            .file-label {
              font-size: 26rpx;
              color: #666;
              display: block;
              margin-bottom: 12rpx;
              text-align: center;
              font-weight: 500;
            }

            .file-name {
              font-size: 28rpx;
              color: #333;
              font-weight: 600;
              word-break: break-all;
              line-height: 1.5;
              display: block;
              text-align: center;
              padding: 8rpx 0;
            }
          }

          .file-warning {
            font-size: 28rpx;
            color: #ff4757;
            font-weight: 600;
            text-align: center;
            margin-top: 8rpx;
            padding: 16rpx;
            background: rgba(255, 71, 87, 0.1);
            border-radius: 8rpx;
            border: 1rpx solid rgba(255, 71, 87, 0.2);
          }
        }
    }
    .dialog-buttons {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20rpx;
      gap: 20rpx;
      margin-bottom: 20rpx;
      
      .button {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        font-size: 32rpx;
        border-radius: 8rpx;
        border: none;
        &.button-hover {
          opacity: 0.8;
        }
      }
      .cancel-btn {
        background: #F5F5F5;
        color: #333333;
      }
      .confirm-btn {
        // background: #FF6B00;
        color: #FFFFFF;
      }
    }
}
}

// 动画效果
@keyframes dialogShow {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.dialog-mask {
  animation: maskShow 0.3s ease-out;
}

@keyframes maskShow {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>