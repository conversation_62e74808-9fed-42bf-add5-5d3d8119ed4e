# VideoUploadCard 组件

视频上传卡片组件，提供美观的视频文件上传界面。

## 功能特性

- 📁 **拖拽上传**：支持点击和拖拽上传视频文件
- 🎬 **视频预览**：显示视频缩略图和播放按钮
- 📊 **上传进度**：实时显示上传进度条
- 📈 **统计信息**：显示已上传文件数量和总大小
- 🎨 **现代化设计**：卡片式布局，渐变色和阴影效果
- ⚙️ **高度可配置**：支持自定义标题、描述、限制等
- 📱 **响应式布局**：适配不同屏幕尺寸

## 使用方法

### 基础用法

```vue
<template>
  <VideoUploadCard
    v-model="videoList"
    @after-read="handleAfterRead"
    @delete="handleDelete"
    @upload="handleUpload"
  />
</template>

<script setup>
import VideoUploadCard from '@/components/platform/video-upload-card.vue'
import { ref } from 'vue'

const videoList = ref([])

function handleAfterRead(file) {
  console.log('文件上传:', file)
}

function handleDelete(file, index) {
  console.log('删除文件:', file, index)
}

function handleUpload() {
  console.log('触发上传')
}
</script>
```

### 自定义配置

```vue
<template>
  <VideoUploadCard
    v-model="videoList"
    title="素材上传"
    subtitle="上传您的视频素材文件"
    upload-text="选择视频文件"
    upload-desc="支持 MP4、MOV、AVI 等格式"
    :max-count="3"
    :is-uploading="isUploading"
    :upload-progress="uploadProgress"
    :show-stats="true"
    @after-read="handleAfterRead"
    @delete="handleDelete"
    @upload="handleUpload"
  />
</template>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | Array | [] | 视频文件列表，支持 v-model |
| title | String | '上传视频' | 卡片标题 |
| subtitle | String | '上传您的视频素材，支持多个文件' | 卡片副标题 |
| uploadText | String | '点击上传视频' | 上传按钮文字 |
| uploadDesc | String | '支持 MP4、MOV、AVI 等格式' | 上传描述文字 |
| maxCount | Number | 5 | 最大上传文件数量 |
| disabled | Boolean | false | 是否禁用上传 |
| showStats | Boolean | true | 是否显示统计信息 |
| isUploading | Boolean | false | 是否正在上传 |
| uploadProgress | Number | 0 | 上传进度 (0-100) |

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:modelValue | 文件列表更新时触发 | (fileList: Array) |
| after-read | 文件选择后触发 | (file: Object) |
| delete | 删除文件时触发 | (file: Object, index: Number) |
| upload | 点击上传按钮时触发 | - |

## 文件对象结构

```typescript
interface VideoFile {
  name: string        // 文件名
  size: number        // 文件大小（字节）
  thumb?: string      // 缩略图URL
  url?: string        // 文件URL
  // ... 其他uview-plus upload组件的属性
}
```

## 样式定制

组件使用了现代化的设计风格：

- **主色调**：蓝色渐变 (#3b82f6 到 #1d4ed8)
- **背景**：浅灰色渐变
- **卡片**：白色背景，圆角阴影
- **交互**：悬停和点击动画效果

### 自定义样式

```scss
// 覆盖组件样式
:deep(.upload-area) {
  .main-upload-btn {
    border-color: #your-color;
    background: your-gradient;
  }
}
```

## 使用场景

1. **视频编辑应用**：上传素材文件
2. **内容管理系统**：批量上传视频
3. **社交媒体平台**：用户内容上传
4. **在线教育平台**：课程视频上传
5. **企业应用**：培训视频管理

## 注意事项

1. 组件依赖 `uview-plus` UI 库的 `up-upload` 组件
2. 需要在项目中正确配置文件上传的后端接口
3. 建议在使用前设置合适的文件大小和数量限制
4. 组件使用了 TypeScript，建议在 TS 项目中使用
5. 确保项目中已正确引入相关的图标字体

## 兼容性

- ✅ H5
- ✅ 小程序
- ✅ APP
- ✅ 支持 Vue 3 Composition API

## 更新日志

### v1.0.0
- 🎉 初始版本发布
- ✨ 支持基础上传功能
- 🎨 现代化UI设计
- 📱 响应式布局
- 📊 上传统计功能
